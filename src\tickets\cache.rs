use std::collections::HashMap;
use std::hash::Hash;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};

/// Cache entry with TTL and access tracking for LRU
#[derive(Debu<PERSON>, Clone)]
struct CacheEntry<V> {
    value: V,
    expires_at: Instant,
    last_accessed: Instant,
}

impl<V> CacheEntry<V> {
    fn new(value: V, ttl: Duration) -> Self {
        let now = Instant::now();
        Self {
            value,
            expires_at: now + ttl,
            last_accessed: now,
        }
    }

    fn is_expired(&self) -> bool {
        Instant::now() > self.expires_at
    }

    fn touch(&mut self) {
        self.last_accessed = Instant::now();
    }
}

/// Generic cache wrapper that provides LRU, TTL, and periodic cleanup
/// around any store that implements the Store trait
pub struct Cache<K, V, S>
where
    K: Hash + Eq + Clone + Send + Sync,
    V: Clone + Send + Sync,
    S: Store<K, V>,
{
    store: S,
    cache: Arc<RwLock<HashMap<K, CacheEntry<V>>>>,
    max_size: usize,
    default_ttl: Duration,
}

/// Trait that the underlying store must implement
pub trait Store<K, V> {
    fn fetch(&self, key: K) -> Option<V>;
    fn insert(&self, key: K, value: V) -> Option<V>;
    fn update(&self, key: K, value: V) -> Option<V>;
    fn remove(&self, key: K) -> Option<V>;
}

impl<K, V, S> Cache<K, V, S>
where
    K: Hash + Eq + Clone + Send + Sync + 'static,
    V: Clone + Send + Sync + 'static,
    S: Store<K, V>,
{
    pub fn new(store: S, max_size: usize, default_ttl: Duration) -> Self {
        let cache = Arc::new(RwLock::new(HashMap::new()));

        Self {
            store,
            cache,
            max_size,
            default_ttl,
        }
    }

    /// Fetch a value, checking cache first, then falling back to store
    pub fn fetch(&self, key: K) -> Option<V> {
        // Try cache first
        if let Some(value) = self.get_from_cache(&key) {
            return Some(value);
        }

        // Fall back to store
        if let Some(value) = self.store.fetch(key.clone()) {
            self.put_in_cache(key, value.clone());
            Some(value)
        } else {
            None
        }
    }

    /// Insert a value into both cache and store
    pub fn insert(&self, key: K, value: V) -> Option<V> {
        let result = self.store.insert(key.clone(), value.clone());
        if result.is_some() {
            self.put_in_cache(key, value);
        }
        result
    }

    /// Update a value in both cache and store
    pub fn update(&self, key: K, value: V) -> Option<V> {
        let result = self.store.update(key.clone(), value.clone());
        if result.is_some() {
            self.put_in_cache(key, value);
        }
        result
    }

    /// Remove a value from both cache and store
    pub fn remove(&self, key: K) -> Option<V> {
        self.remove_from_cache(&key);
        self.store.remove(key)
    }

    /// Start periodic cleanup task for this cache instance
    pub fn start_cleanup_task(&self) {
        let cache = Arc::clone(&self.cache);
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60)); // Clean every minute
            loop {
                interval.tick().await;
                if let Ok(mut cache_guard) = cache.write() {
                    cache_guard.retain(|_, entry| !entry.is_expired());
                }
            }
        });
    }

    /// Get value from cache if it exists and is not expired
    fn get_from_cache(&self, key: &K) -> Option<V> {
        if let Ok(mut cache) = self.cache.write() {
            if let Some(entry) = cache.get_mut(key) {
                if entry.is_expired() {
                    cache.remove(key);
                    None
                } else {
                    entry.touch();
                    Some(entry.value.clone())
                }
            } else {
                None
            }
        } else {
            None
        }
    }

    /// Put value in cache, handling LRU eviction if needed
    fn put_in_cache(&self, key: K, value: V) {
        if let Ok(mut cache) = self.cache.write() {
            // If at capacity, remove LRU item
            if cache.len() >= self.max_size {
                self.evict_lru(&mut cache);
            }

            cache.insert(key, CacheEntry::new(value, self.default_ttl));
        }
    }

    /// Remove value from cache
    fn remove_from_cache(&self, key: &K) {
        if let Ok(mut cache) = self.cache.write() {
            cache.remove(key);
        }
    }

    /// Evict least recently used item
    fn evict_lru(&self, cache: &mut HashMap<K, CacheEntry<V>>) {
        if let Some((lru_key, _)) = cache
            .iter()
            .min_by_key(|(_, entry)| entry.last_accessed)
            .map(|(k, v)| (k.clone(), v.clone()))
        {
            cache.remove(&lru_key);
        }
    }
}
