use std::collections::HashMap;
use std::hash::Hash;
use std::time::{Duration, Instant};

/// Cache entry with TTL and access tracking for LRU
#[derive(Debug, Clone)]
struct CacheEntry<V> {
    value: V,
    expires_at: Instant,
    last_accessed: Instant,
}

impl<V> CacheEntry<V> {
    fn new(value: V, ttl: Duration) -> Self {
        let now = Instant::now();
        Self {
            value,
            expires_at: now + ttl,
            last_accessed: now,
        }
    }

    fn is_expired(&self) -> bool {
        Instant::now() > self.expires_at
    }

    fn touch(&mut self) {
        self.last_accessed = Instant::now();
    }
}

/// Cache wrapper that acts like a HashMap but provides LRU, TTL, and periodic cleanup
pub struct Cache<K, V>
where
    K: Hash + Eq + Clone,
    V: Clone,
{
    inner: HashMap<K, CacheEntry<V>>,
    max_size: usize,
    default_ttl: Duration,
}

impl<K, V> Cache<K, V>
where
    K: Hash + Eq + <PERSON>lone,
    V: Clone,
{
    pub fn new() -> Self {
        Self {
            inner: HashMap::new(),
            max_size: 1000,                        // Default max size
            default_ttl: Duration::from_secs(300), // Default 5 minutes TTL
        }
    }

    pub fn with_config(max_size: usize, default_ttl: Duration) -> Self {
        Self {
            inner: HashMap::new(),
            max_size,
            default_ttl,
        }
    }

    /// Clean up expired entries
    fn cleanup_expired(&mut self) {
        self.inner.retain(|_, entry| !entry.is_expired());
    }

    /// Evict least recently used item if at capacity
    fn evict_lru_if_needed(&mut self) {
        if self.inner.len() >= self.max_size {
            if let Some((lru_key, _)) = self
                .inner
                .iter()
                .min_by_key(|(_, entry)| entry.last_accessed)
                .map(|(k, v)| (k.clone(), v.clone()))
            {
                self.inner.remove(&lru_key);
            }
        }
    }

    // HashMap-like interface methods
    pub fn get(&mut self, key: &K) -> Option<&V> {
        self.cleanup_expired();

        if let Some(entry) = self.inner.get_mut(key) {
            if entry.is_expired() {
                self.inner.remove(key);
                None
            } else {
                entry.touch();
                Some(&entry.value)
            }
        } else {
            None
        }
    }

    pub fn get_mut(&mut self, key: &K) -> Option<&mut V> {
        self.cleanup_expired();

        if let Some(entry) = self.inner.get_mut(key) {
            if entry.is_expired() {
                self.inner.remove(key);
                None
            } else {
                entry.touch();
                Some(&mut entry.value)
            }
        } else {
            None
        }
    }

    pub fn insert(&mut self, key: K, value: V) -> Option<V> {
        self.cleanup_expired();
        self.evict_lru_if_needed();

        let entry = CacheEntry::new(value, self.default_ttl);
        self.inner
            .insert(key, entry)
            .map(|old_entry| old_entry.value)
    }

    pub fn remove(&mut self, key: &K) -> Option<V> {
        self.inner.remove(key).map(|entry| entry.value)
    }

    pub fn contains_key(&mut self, key: &K) -> bool {
        self.cleanup_expired();

        if let Some(entry) = self.inner.get(key) {
            if entry.is_expired() {
                self.inner.remove(key);
                false
            } else {
                true
            }
        } else {
            false
        }
    }

    pub fn len(&self) -> usize {
        self.inner.len()
    }

    pub fn is_empty(&self) -> bool {
        self.inner.is_empty()
    }

    pub fn clear(&mut self) {
        self.inner.clear();
    }
}
