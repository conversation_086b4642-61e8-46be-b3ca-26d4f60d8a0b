use serenity::all::{
    ChannelId, ChannelType, CreateChannel, GuildChannel, GuildId, Http, PermissionOverwrite,
    PermissionOverwriteType, UserId,
};

use crate::permissions;

pub async fn create_channel(
    http: &Http,
    guild_id: GuildId,
    user_id: UserId,
    category_id: ChannelId,
) -> Option<GuildChannel> {
    let guild = http.get_guild(GuildId::new(guild_id.into())).await.ok()?;

    let everyone_permissions = PermissionOverwrite {
        allow: permissions::ticket_channel::EVERYONE_ALLOW,
        deny: permissions::ticket_channel::EVERYONE_DENY,
        kind: PermissionOverwriteType::Role(guild.id.everyone_role()),
    };

    let user_permissions = PermissionOverwrite {
        allow: permissions::ticket_channel::USER_ALLOW,
        deny: permissions::ticket_channel::USER_DENY,
        kind: PermissionOverwriteType::Member(UserId::new(user_id.into())),
    };

    let builder = CreateChannel::new(user_id.to_string())
        .kind(ChannelType::Text)
        .category(ChannelId::new(category_id.into()))
        .permissions(vec![everyone_permissions, user_permissions]);

    guild.create_channel(http, builder).await.ok()
}
