use serenity::all::{
    ChannelId, ChannelType, CreateChannel, GuildChannel, GuildId, Http, PermissionOverwrite,
    PermissionOverwriteType, UserId,
};

use crate::{models::Snowflake, permissions};

pub async fn create_channel(
    http: &Http,
    guild_id: Snowflake,
    user_id: Snowflake,
    category_id: Snowflake,
) -> Option<GuildChannel> {
    let guild = match http.get_guild(GuildId::new(guild_id.into())).await.ok() {
        Some(guild) => guild,
        None => return None,
    };

    let everyone_permissions = PermissionOverwrite {
        allow: permissions::ticket_channel::EVERYONE_ALLOW,
        deny: permissions::ticket_channel::EVERYONE_DENY,
        kind: PermissionOverwriteType::Role(guild.id.everyone_role()),
    };

    let user_permissions = PermissionOverwrite {
        allow: permissions::ticket_channel::USER_ALLOW,
        deny: permissions::ticket_channel::USER_DENY,
        kind: PermissionOverwriteType::Member(UserId::new(user_id.into())),
    };

    let builder = CreateChannel::new(user_id.to_string())
        .kind(ChannelType::Text)
        .category(ChannelId::new(category_id.into()))
        .permissions(vec![everyone_permissions, user_permissions]);

    guild.create_channel(http, builder).await.ok()
}
