use crate::models::<PERSON>f<PERSON>;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct Ticket {
    pub user_id: Snowflake,
    pub guild_id: Snowflake,
    pub category_id: Snowflake,
    pub channel_id: Snowflake,
    pub contact_id: String,
    pub contact_name: String,
}

impl Ticket {
    pub fn encode(&self) -> String {
        format!(
            "ticket | {} | {} | {} | {} | {} | {}",
            self.guild_id,
            self.category_id,
            self.channel_id,
            self.user_id,
            self.contact_id,
            self.contact_name
        )
    }

    pub fn decode(encoded: &str) -> Option<Self> {
        let mut split = encoded.split(" | ");

        let guild_id = split.next()?;
        let category_id = split.next()?;
        let channel_id = split.next()?;
        let user_id = split.next()?;
        let contact_id = split.next()?;
        let contact_name = split.next()?;

        Some(Ticket {
            user_id: user_id.parse().ok()?,
            guild_id: guild_id.parse().ok()?,
            category_id: category_id.parse().ok()?,
            channel_id: channel_id.parse().ok()?,
            contact_id: contact_id.to_string(),
            contact_name: contact_name.to_string(),
        })
    }
}

#[derive(Debug, Clone, Default)]
pub struct TicketInsert {
    pub user_id: Snowflake,
    pub guild_id: Snowflake,
    pub category_id: Snowflake,
    pub channel_id: Snowflake,
    pub contact_id: String,
    pub contact_name: String,
}

#[derive(Debug, Clone, Default)]
pub struct TicketChangeset {
    pub guild_id: Option<Snowflake>,
    pub category_id: Option<Snowflake>,
    pub channel_id: Option<Snowflake>,
    pub contact_name: Option<String>,
}
