use serenity::model::id::*;

use crate::trengo::models::{ContractId, ContractName};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct Mailbox {
    pub category_id: ChannelId,
    pub channel_id: ChannelId,
}

impl Mailbox {
    pub fn encode(&self) -> String {
        format!("mailbox | {} | {}", self.category_id, self.channel_id)
    }

    pub fn decode(encoded: &str) -> Option<Self> {
        let mut split = encoded.split(" | ");

        let tag = split.next()?;

        if tag != "mailbox" {
            return None;
        }

        let category_id = split.next()?;
        let channel_id = split.next()?;

        Some(Self {
            category_id: category_id.parse().ok()?,
            channel_id: channel_id.parse().ok()?,
        })
    }
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct Ticket {
    pub user_id: UserId,
    pub channel_id: ChannelId,
    pub category_id: ChannelId,
    pub contact_id: ContractId,
    pub contact_name: ContractName,
}

impl Ticket {
    pub fn encode(&self) -> String {
        format!(
            "ticket | {} | {} | {} | {} | {}",
            self.user_id, self.channel_id, self.category_id, self.contact_id, self.contact_name
        )
    }

    pub fn decode(encoded: &str) -> Option<Self> {
        let mut split = encoded.split(" | ");

        let tag = split.next()?;

        if tag != "ticket" {
            return None;
        }

        let user_id = split.next()?;
        let channel_id = split.next()?;
        let category_id = split.next()?;
        let contact_id = split.next()?;
        let contact_name = split.next()?;

        Some(Self {
            user_id: user_id.parse().ok()?,
            channel_id: channel_id.parse().ok()?,
            category_id: category_id.parse().ok()?,
            contact_id: contact_id.into(),
            contact_name: contact_name.into(),
        })
    }
}

#[derive(Debug, Clone, Default)]
pub struct TicketInsert {
    pub user_id: UserId,
    pub channel_id: ChannelId,
    pub category_id: ChannelId,
    pub contact_id: ContractId,
    pub contact_name: ContractName,
}

#[derive(Debug, Clone, Default)]
pub struct TicketChangeset {
    pub category_id: Option<ChannelId>,
    pub channel_id: Option<ChannelId>,
    pub contact_name: Option<ContractName>,
}
