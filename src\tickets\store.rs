use once_cell::sync::Lazy;
use std::collections::HashMap;
use std::sync::RwLock;

use super::cache::Store;
use super::{Ticket, TicketChangeset};
use crate::{models::Snowflake, tickets::models::TicketInsert};

pub struct TicketsStore {
    tickets: RwLock<HashMap<Snowflake, Ticket>>,
}

impl TicketsStore {
    pub fn new() -> Self {
        Self {
            tickets: RwLock::new(HashMap::new()),
        }
    }

    /// Legacy method for inserting from TicketInsert - converts to Store trait call
    pub fn insert_from_values(&self, values: TicketInsert) -> Option<Ticket> {
        // Convert TicketInsert to Ticket
        let ticket = Ticket {
            user_id: values.user_id,
            guild_id: values.guild_id,
            category_id: values.category_id,
            channel_id: values.channel_id,
            contact_id: values.contact_id,
            contact_name: values.contact_name,
        };

        // Use the Store trait implementation
        self.insert(values.user_id, ticket)
    }

    /// Legacy method for updating with changeset
    pub fn update_with_changeset(
        &self,
        id: Snowflake,
        changeset: TicketChangeset,
    ) -> Option<Ticket> {
        if let Ok(mut tickets) = self.tickets.write() {
            if let Some(mut ticket) = tickets.get(&id).cloned() {
                // Apply changeset to ticket
                // TODO: implement changeset application logic

                // Use the Store trait implementation
                drop(tickets); // Release the lock before calling self.update
                self.update(id, ticket)
            } else {
                None
            }
        } else {
            None
        }
    }
}

impl Store<Snowflake, Ticket> for TicketsStore {
    fn fetch(&self, key: Snowflake) -> Option<Ticket> {
        if let Ok(tickets) = self.tickets.read() {
            match tickets.get(&key) {
                Some(ticket) => Some(ticket.clone()),
                None => {
                    // TODO: use channel topics and guild_id to search for ticket then store it in cache.
                    None
                }
            }
        } else {
            None
        }
    }

    fn insert(&self, key: Snowflake, value: Ticket) -> Option<Ticket> {
        if let Ok(mut tickets) = self.tickets.write() {
            if tickets.contains_key(&key) {
                return None;
            }

            // TODO: insert ticket into both store and channel topics.
            tickets.insert(key, value.clone());
            Some(value)
        } else {
            None
        }
    }

    fn update(&self, key: Snowflake, value: Ticket) -> Option<Ticket> {
        if let Ok(mut tickets) = self.tickets.write() {
            if tickets.contains_key(&key) {
                // TODO: update ticket in both store and channel topics.
                tickets.insert(key, value.clone());
                Some(value)
            } else {
                None
            }
        } else {
            None
        }
    }

    fn remove(&self, key: Snowflake) -> Option<Ticket> {
        if let Ok(mut tickets) = self.tickets.write() {
            tickets.remove(&key)
        } else {
            None
        }
    }
}

pub static TICKETS_STORE: Lazy<TicketsStore> = Lazy::new(TicketsStore::new);
