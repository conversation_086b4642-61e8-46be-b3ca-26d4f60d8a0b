use serenity::async_trait;
use serenity::model::channel::{ChannelType, Message};
use serenity::prelude::*;
use tracing::{error, info, warn};

use crate::{
    ENV, regex,
    tickets::TICKETS,
    trengo::{self, StoreCustomChannelMessageBody},
};

pub struct Handler;

#[async_trait]
impl EventHandler for Handler {
    async fn message(&self, ctx: Context, msg: Message) {
        if msg.author.bot {
            return;
        }

        let guild_id = match msg.guild_id {
            Some(id) => id,
            None => {
                warn!("unavailable guild id for possibly received ticket message");

                return;
            }
        };

        let channel_name = {
            let guild = match ctx.cache.guild(guild_id) {
                Some(guild) => guild,
                None => {
                    warn!(
                        "unavailable guild with id: {} for possibly received ticket message",
                        guild_id
                    );

                    return;
                }
            };

            let channel = match guild.channels.get(&msg.channel_id) {
                Some(channel) => channel,
                None => {
                    warn!(
                        "unavailable possible ticket channel with id: {} for possibly received ticket message",
                        msg.channel_id
                    );

                    return;
                }
            };

            if channel.kind != ChannelType::Text || !channel.name.chars().all(|c| c.is_numeric()) {
                return;
            }

            channel.name.clone()
        };

        if !regex::SNOWFLAKE.is_match(&channel_name) {
            return;
        }

        let user_id = match channel_name.parse::<u64>() {
            Ok(id) => id,
            Err(error) => {
                error!(
                    "error encountered while parsing user id from channel name while transporting message to trengo: {:?}",
                    error
                );

                return;
            }
        };

        let (contact_identifier, contact_name) = {
            match TICKETS.fetch(user_id.into()) {
                Some(ticket) => (ticket.contact_id.clone(), ticket.contact_name.clone()),
                None => {
                    warn!(
                        "request for transport of message to trengo was received for a user that has no ticket created in the system for user with id: {}",
                        user_id
                    );

                    return;
                }
            }
        };

        info!(
            "recv request for transport of message to trengo for user with id: {}",
            user_id
        );

        if !msg.content.is_empty() {
            let body = StoreCustomChannelMessageBody {
                channel: ENV.trengo_custom_channel_token.clone(),
                contact_identifier: contact_identifier.clone(),
                contact_name: contact_name.clone(),
                content: msg.content,
            };

            if let None = trengo::store_custom_channel_message(body).await {
                error!(
                    "error while transporting message to trengo for user with id: {}",
                    user_id
                );
            }
        }

        for attachment in msg.attachments {
            let body = StoreCustomChannelMessageBody {
                channel: ENV.trengo_custom_channel_token.clone(),
                contact_identifier: contact_identifier.clone(),
                contact_name: contact_name.clone(),
                content: attachment.url,
            };

            if let None = trengo::store_custom_channel_message(body).await {
                error!(
                    "error while transporting attachment to trengo for user with id: {}",
                    user_id
                );
            }
        }
    }
}
