pub mod collection;
pub mod models;
pub mod utils;

use once_cell::sync::Lazy;
use std::collections::HashMap;
use std::sync::RwLock;

use models::{Ticket, TicketChangeset};

use crate::{models::Snowflake, tickets::models::TicketInsert};

pub struct Tickets {
    tickets: RwLock<HashMap<Snowflake, Ticket>>,
}

impl Tickets {
    pub fn new() -> Self {
        Self {
            tickets: RwLock::new(HashMap::new()),
        }
    }

    /// fetches a ticket by user id snowflake.
    pub fn fetch(&self, id: Snowflake) -> Option<Ticket> {
        if let Ok(tickets) = self.tickets.write() {
            match tickets.get(&id) {
                Some(ticket) => Some(ticket.clone()),
                None => {
                    // TODO: use channel topics and guild_id to search for ticket then store it in cache.

                    None
                }
            }
        } else {
            None
        }
    }

    pub fn insert(&self, values: TicketInsert) -> Option<Ticket> {
        if let Ok(tickets) = self.tickets.write() {
            if tickets.contains_key(&values.user_id) {
                return None;
            }

            // TODO: insert ticket into both store and channel topics.
        }

        None
    }

    pub fn update(&self, id: Snowflake, _changeset: TicketChangeset) -> Option<Ticket> {
        if let Ok(tickets) = self.tickets.write() {
            if tickets.contains_key(&id) {
                // TODO: update ticket in both store and channel topics.
            }
        }

        None
    }
}

pub static TICKETS: Lazy<Tickets> = Lazy::new(Tickets::new);
