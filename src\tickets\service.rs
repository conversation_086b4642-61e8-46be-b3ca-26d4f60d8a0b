use lru::LruCache;
use serenity::all::Channel;
use serenity::all::EditChannel;
use serenity::all::GuildId;
use serenity::all::UserId;
use std::num::NonZeroUsize;
use std::sync::Arc;
use tokio::sync::RwLock;

use super::{Ticket, TicketChangeset};
use crate::tickets::Mailbox;
use crate::tickets::models::TicketInsert;

#[derive(Debug)]
pub struct TicketsService {
    discord_http: Arc<serenity::http::Http>,
    mailbox: Option<Mailbox>,
    tickets: RwLock<LruCache<UserId, Ticket>>,
    guild_id: GuildId,
}

impl TicketsService {
    pub fn new(http: Arc<serenity::http::Http>, guild_id: GuildId, lru_capacity: usize) -> Self {
        Self {
            discord_http: http,
            mailbox: None,
            tickets: RwLock::new(LruCache::new(NonZeroUsize::new(lru_capacity).unwrap())),
            guild_id: guild_id,
        }
    }

    pub async fn fetch_tickets_mailbox(&self) -> Option<Mailbox> {
        if self.mailbox.is_some() {
            return self.mailbox.clone();
        }

        let guild = self.discord_http.get_guild(self.guild_id).await.ok()?;

        let channels = guild.channels(&self.discord_http).await.ok()?;

        let mailbox = channels
            .values()
            .filter(|channel| channel.topic.is_some())
            .find(|channel| channel.topic.as_ref().unwrap().starts_with("mailbox | "))?;

        Mailbox::decode(&mailbox.topic.as_ref().unwrap())
    }

    pub async fn fetch(&self, user_id: UserId) -> Option<Ticket> {
        let mut tickets = self.tickets.write().await;

        if let Some(ticket) = tickets.get(&user_id) {
            return Some(ticket.clone());
        }

        if let Some(ticket) = self
            .discord_http
            .get_guild(self.guild_id)
            .await
            .ok()?
            .channels(&self.discord_http)
            .await
            .ok()?
            .values()
            .filter(|channel| channel.topic.is_some())
            .filter(|channel| channel.topic.as_ref().unwrap().starts_with("ticket | "))
            .find(|channel| channel.name == user_id.to_string())
            .map(|channel| Ticket::decode(&channel.topic.as_ref().unwrap()))
            .flatten()
        {
            tickets.put(ticket.user_id, ticket.clone());

            return Some(ticket);
        }

        None
    }

    pub async fn insert(&self, values: TicketInsert) -> Option<Ticket> {
        let mut tickets = self.tickets.write().await;

        if tickets.contains(&values.user_id) {
            return None;
        }

        let inserted = Ticket {
            user_id: values.user_id,
            channel_id: values.channel_id,
            category_id: values.category_id,
            contact_id: values.contact_id,
            contact_name: values.contact_name,
        };

        tickets.put(values.user_id, inserted.clone());

        Some(inserted)
    }

    pub async fn update(&self, user_id: UserId, changeset: TicketChangeset) -> Option<Ticket> {
        let ticket = self.fetch(user_id).await?;

        let updated = Ticket {
            user_id: ticket.user_id,
            channel_id: changeset.channel_id.unwrap_or(ticket.channel_id),
            category_id: changeset.category_id.unwrap_or(ticket.category_id),
            contact_id: ticket.contact_id,
            contact_name: changeset.contact_name.unwrap_or(ticket.contact_name),
        };

        let mut channel = self
            .discord_http
            .get_channel(updated.channel_id.into())
            .await
            .ok()
            .map(|channel| match channel {
                Channel::Guild(channel) => Some(channel),
                _ => return None,
            })
            .flatten()?;

        let mut tickets = self.tickets.write().await;

        channel
            .edit(
                &self.discord_http,
                EditChannel::new().topic(updated.encode()),
            )
            .await
            .ok()?;

        tickets.put(updated.user_id, updated.clone());

        Some(updated)
    }
}
