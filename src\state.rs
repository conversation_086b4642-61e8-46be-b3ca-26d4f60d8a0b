use std::sync::Arc;

use serenity::{all::GuildId, prelude::TypeMapKey};

use crate::tickets::TicketsService;

#[derive(Debug)]
pub struct RocketState {
    pub discord_http: Arc<serenity::http::Http>,
    pub tickets: Arc<TicketsService>,
    pub guild_id: GuildId,
}

#[derive(Debug)]
pub struct SerenityState {
    pub tickets: Arc<TicketsService>,
    pub guild_id: GuildId,
}

impl TypeMapKey for SerenityState {
    type Value = SerenityState;
}
