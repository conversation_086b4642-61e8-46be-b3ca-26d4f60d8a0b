use serenity::all::GuildId;
use serenity::all::Interaction;
use serenity::all::Ready;
use serenity::async_trait;
use serenity::prelude::*;
use tracing::error;
use tracing::info;

use crate::ENV;
use crate::commands;

pub struct Handler;

#[async_trait]
impl EventHandler for <PERSON><PERSON> {
    async fn interaction_create(&self, ctx: Context, interaction: Interaction) {
        if let Interaction::Command(interaction) = &interaction {
            let options = &interaction.data.options();

            let response = match interaction.data.name.as_str() {
                "set-mailbox" => {
                    info!("recv set-mailbox command request");

                    commands::set_mailbox::run(&ctx, interaction, options).await
                }
                _ => unimplemented!(),
            };

            if let Err(why) = interaction.create_response(&ctx.http, response).await {
                error!(
                    "error encountered while creating interaction response for command: {:?}",
                    why
                );
            }
        }
    }

    async fn ready(&self, ctx: Context, _ready: Ready) {
        let guild_id = GuildId::new(ENV.discord_guild_id);

        let commands = vec![commands::set_mailbox::register()];

        for command in &commands {
            info!(
                "added command to list of commands to register: {:?}",
                command
            );
        }

        guild_id.set_commands(&ctx.http, commands).await.ok();

        info!("registered all commands in guild with id: {}", guild_id);
    }
}
