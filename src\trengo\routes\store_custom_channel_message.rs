use serde::Serialize;

use crate::trengo::{BASE_URL, CLIENT};

#[derive(Debug, Serialize)]
pub struct StoreCustomChannelMessageBody {
    pub channel: String,
    pub contact_identifier: String,
    pub contact_name: String,
    pub content: String,
}

/// sends a message to a custom channel in trengo.
///
/// # Returns
///
/// Some if the message was sent successfully.
///
/// None if the request failed for any reason.
pub async fn store_custom_channel_message(body: StoreCustomChannelMessageBody) -> Option<()> {
    CLIENT
        .post(format!("{BASE_URL}/custom_channel_messages").as_str())
        .json(&serde_json::json!({
          "channel": body.channel,
          "contact": {
            "identifier": body.contact_identifier,
            "name": body.contact_name,
          },
          "body": {
            "text": body.content
          },
        }))
        .send()
        .await
        .ok()
        .map(|_| ())
}
