use std::collections::HashMap;
use std::hash::Hash;
use std::sync::RwLock;

pub struct Collection<K: Eq + Hash, V: Clone> {
    map: RwLock<HashMap<K, V>>,
}

impl<K: Eq + Hash, V: Clone> Collection<K, V> {
    pub fn new() -> Self {
        Self {
            map: RwLock::new(HashMap::new()),
        }
    }

    pub fn fetch(&self, key: K) -> Option<V> {
        if let Ok(map) = self.map.read() {
            return match map.get(&key) {
                Some(value) => Some(value.clone()),
                None => None,
            };
        }

        None
    }
}
