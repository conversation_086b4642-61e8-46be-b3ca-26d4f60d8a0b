use serenity::all::{
    Channel, ChannelType, CommandInteraction, CommandOptionType, Context, CreateCommand,
    CreateCommandOption, CreateInteractionResponse, CreateInteractionResponseMessage,
    CreateMessage, EditChannel, GetMessages, InteractionResponseFlags, PermissionOverwrite,
    PermissionOverwriteType, Permissions, ResolvedOption, ResolvedValue,
};
use tracing::{error, info};

use crate::components::{
    MAILBOX_CHANNEL_CONFIGURED_MESSAGE, create_ticket_embed, ticket_create_button,
};

pub async fn run<'a>(
    ctx: &'a Context,
    interaction: &CommandInteraction,
    options: &'a [ResolvedOption<'a>],
) -> CreateInteractionResponse {
    let mut channel = {
        let channel_id = match {
            if let Some(ResolvedOption {
                value: ResolvedValue::Channel(channel),
                ..
            }) = options.first()
            {
                Some(channel.id)
            } else {
                None
            }
        } {
            Some(channel_id) => channel_id,
            None => {
                return CreateInteractionResponse::Message(
                    CreateInteractionResponseMessage::new()
                        .content("please provide a channel to create tickets from")
                        .flags(InteractionResponseFlags::EPHEMERAL),
                );
            }
        };

        match ctx.http.get_channel(channel_id).await.ok() {
            Some(channel) => match channel {
                Channel::Guild(channel) => channel,
                _ => {
                    error!(
                        "unavailable channel with id: {} for set-mailbox command",
                        channel_id
                    );

                    return CreateInteractionResponse::Message(
                        CreateInteractionResponseMessage::new()
                            .content("unexpected error occurred!")
                            .flags(InteractionResponseFlags::EPHEMERAL),
                    );
                }
            },
            None => {
                error!(
                    "unavailable channel with id: {} for set-mailbox command",
                    channel_id
                );

                return CreateInteractionResponse::Message(
                    CreateInteractionResponseMessage::new()
                        .content("unexpected error occurred!")
                        .flags(InteractionResponseFlags::EPHEMERAL),
                );
            }
        }
    };

    let category_id = match channel.parent_id {
        Some(id) => id,
        None => {
            error!(
                "unavailable parent id for channel with id: {} for set-mailbox command",
                channel.id
            );

            return CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content("unexpected error occurred!")
                    .flags(InteractionResponseFlags::EPHEMERAL),
            );
        }
    };

    match channel
        .edit(
            &ctx.http,
            EditChannel::new().topic(format!("mailbox | {}", category_id)),
        )
        .await
    {
        Ok(_) => {
            info!(
                "configured mailbox channel for guild with id: {}",
                channel.guild_id
            );
        }
        Err(why) => {
            error!(
                "error while updating channel with id: {} for set-mailbox command: {:?}",
                channel.id, why
            );

            return CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content("unexpected error occurred!")
                    .flags(InteractionResponseFlags::EPHEMERAL),
            );
        }
    }

    if let Err(why) = channel
        .create_permission(
            &ctx.http,
            PermissionOverwrite {
                allow: Permissions::empty(),
                deny: Permissions::SEND_MESSAGES,
                kind: PermissionOverwriteType::Role(channel.guild_id.everyone_role()),
            },
        )
        .await
    {
        error!(
            "error while updating permissions of channel mailbox: {:?}",
            why
        );

        return CreateInteractionResponse::Message(
            CreateInteractionResponseMessage::new()
                .content("unexpected error occurred!")
                .flags(InteractionResponseFlags::EPHEMERAL),
        );
    }

    if let Some(messages) = channel
        .messages(&ctx.http, GetMessages::default())
        .await
        .ok()
    {
        channel.delete_messages(&ctx.http, messages).await.ok();
    }

    channel
        .send_message(
            &ctx.http,
            CreateMessage::new()
                .add_embed(create_ticket_embed())
                .button(ticket_create_button()),
        )
        .await
        .ok();

    if let Some(guild_id) = interaction.guild_id {
        info!("configured mailbox channel for guild with id: {}", guild_id);
    }

    CreateInteractionResponse::Message(
        CreateInteractionResponseMessage::new()
            .content(MAILBOX_CHANNEL_CONFIGURED_MESSAGE)
            .flags(InteractionResponseFlags::EPHEMERAL),
    )
}

pub fn register() -> CreateCommand {
    CreateCommand::new("set-mailbox")
        .description("configure's the mailbox channel")
        .add_option(
            CreateCommandOption::new(
                CommandOptionType::Channel,
                "mailbox",
                "the channel to create tickets from",
            )
            .channel_types(vec![ChannelType::Text])
            .required(true),
        )
        .default_member_permissions(Permissions::ADMINISTRATOR)
}
