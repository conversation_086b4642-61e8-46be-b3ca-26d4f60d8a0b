use serde::{Deserialize, Deserializer, Serialize, Serializer};
use serenity::model::id::*;
use std::{fmt::Display, num::ParseIntError, str::FromStr};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, <PERSON>q, <PERSON>ialOrd, Ord, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct Snowflake(u64);

impl Snowflake {
    pub fn new(id: u64) -> Self {
        Self(id)
    }

    pub const fn get(self) -> u64 {
        self.0
    }
}

impl Display for Snowflake {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl From<u64> for Snowflake {
    fn from(snowflake: u64) -> Self {
        Self(snowflake)
    }
}

impl From<Snowflake> for u64 {
    fn from(snowflake: Snowflake) -> Self {
        snowflake.0
    }
}

impl From<Snowflake> for String {
    fn from(snowflake: Snowflake) -> Self {
        snowflake.0.to_string()
    }
}

impl From<String> for Snowflake {
    fn from(snowflake: String) -> Self {
        Self(snowflake.parse::<u64>().unwrap())
    }
}

impl From<&str> for Snowflake {
    fn from(snowflake: &str) -> Self {
        Self(snowflake.parse::<u64>().unwrap())
    }
}

impl FromStr for Snowflake {
    type Err = ParseIntError;

    fn from_str(snowflake: &str) -> Result<Self, Self::Err> {
        Ok(Self(snowflake.parse::<u64>()?))
    }
}

impl From<AttachmentId> for Snowflake {
    fn from(attachment_id: AttachmentId) -> Self {
        Self(attachment_id.get())
    }
}

impl From<Snowflake> for AttachmentId {
    fn from(snowflake: Snowflake) -> Self {
        AttachmentId::new(snowflake.0)
    }
}

impl From<ApplicationId> for Snowflake {
    fn from(application_id: ApplicationId) -> Self {
        Self(application_id.get())
    }
}

impl From<Snowflake> for ApplicationId {
    fn from(snowflake: Snowflake) -> Self {
        ApplicationId::new(snowflake.0)
    }
}

impl From<ChannelId> for Snowflake {
    fn from(channel_id: ChannelId) -> Self {
        Self(channel_id.get())
    }
}

impl From<Snowflake> for ChannelId {
    fn from(snowflake: Snowflake) -> Self {
        ChannelId::new(snowflake.0)
    }
}

impl From<EmojiId> for Snowflake {
    fn from(emoji_id: EmojiId) -> Self {
        Self(emoji_id.get())
    }
}

impl From<Snowflake> for EmojiId {
    fn from(snowflake: Snowflake) -> Self {
        EmojiId::new(snowflake.0)
    }
}

impl From<GenericId> for Snowflake {
    fn from(generic_id: GenericId) -> Self {
        Self(generic_id.get())
    }
}

impl From<Snowflake> for GenericId {
    fn from(snowflake: Snowflake) -> Self {
        GenericId::new(snowflake.0)
    }
}

impl From<GuildId> for Snowflake {
    fn from(guild_id: GuildId) -> Self {
        Self(guild_id.get())
    }
}

impl From<Snowflake> for GuildId {
    fn from(snowflake: Snowflake) -> Self {
        GuildId::new(snowflake.0)
    }
}

impl From<IntegrationId> for Snowflake {
    fn from(integration_id: IntegrationId) -> Self {
        Self(integration_id.get())
    }
}

impl From<Snowflake> for IntegrationId {
    fn from(snowflake: Snowflake) -> Self {
        IntegrationId::new(snowflake.0)
    }
}

impl From<MessageId> for Snowflake {
    fn from(message_id: MessageId) -> Self {
        Self(message_id.get())
    }
}

impl From<Snowflake> for MessageId {
    fn from(snowflake: Snowflake) -> Self {
        MessageId::new(snowflake.0)
    }
}

impl From<RoleId> for Snowflake {
    fn from(role_id: RoleId) -> Self {
        Self(role_id.get())
    }
}

impl From<Snowflake> for RoleId {
    fn from(snowflake: Snowflake) -> Self {
        RoleId::new(snowflake.0)
    }
}

impl From<ScheduledEventId> for Snowflake {
    fn from(scheduled_event_id: ScheduledEventId) -> Self {
        Self(scheduled_event_id.get())
    }
}

impl From<Snowflake> for ScheduledEventId {
    fn from(snowflake: Snowflake) -> Self {
        ScheduledEventId::new(snowflake.0)
    }
}

impl From<StickerId> for Snowflake {
    fn from(sticker_id: StickerId) -> Self {
        Self(sticker_id.get())
    }
}

impl From<Snowflake> for StickerId {
    fn from(snowflake: Snowflake) -> Self {
        StickerId::new(snowflake.0)
    }
}

impl From<StickerPackId> for Snowflake {
    fn from(sticker_pack_id: StickerPackId) -> Self {
        Self(sticker_pack_id.get())
    }
}

impl From<Snowflake> for StickerPackId {
    fn from(snowflake: Snowflake) -> Self {
        StickerPackId::new(snowflake.0)
    }
}

impl From<StickerPackBannerId> for Snowflake {
    fn from(sticker_pack_banner_id: StickerPackBannerId) -> Self {
        Self(sticker_pack_banner_id.get())
    }
}

impl From<Snowflake> for StickerPackBannerId {
    fn from(snowflake: Snowflake) -> Self {
        StickerPackBannerId::new(snowflake.0)
    }
}

impl From<SkuId> for Snowflake {
    fn from(sku_id: SkuId) -> Self {
        Self(sku_id.get())
    }
}

impl From<Snowflake> for SkuId {
    fn from(snowflake: Snowflake) -> Self {
        SkuId::new(snowflake.0)
    }
}

impl From<UserId> for Snowflake {
    fn from(user_id: UserId) -> Self {
        Self(user_id.get())
    }
}

impl From<Snowflake> for UserId {
    fn from(snowflake: Snowflake) -> Self {
        UserId::new(snowflake.0)
    }
}

impl From<WebhookId> for Snowflake {
    fn from(webhook_id: WebhookId) -> Self {
        Self(webhook_id.get())
    }
}

impl From<Snowflake> for WebhookId {
    fn from(snowflake: Snowflake) -> Self {
        WebhookId::new(snowflake.0)
    }
}

impl From<AuditLogEntryId> for Snowflake {
    fn from(audit_log_entry_id: AuditLogEntryId) -> Self {
        Self(audit_log_entry_id.get())
    }
}

impl From<Snowflake> for AuditLogEntryId {
    fn from(snowflake: Snowflake) -> Self {
        AuditLogEntryId::new(snowflake.0)
    }
}

impl From<InteractionId> for Snowflake {
    fn from(interaction_id: InteractionId) -> Self {
        Self(interaction_id.get())
    }
}

impl From<Snowflake> for InteractionId {
    fn from(snowflake: Snowflake) -> Self {
        InteractionId::new(snowflake.0)
    }
}

impl From<CommandId> for Snowflake {
    fn from(command_id: CommandId) -> Self {
        Self(command_id.get())
    }
}

impl From<Snowflake> for CommandId {
    fn from(snowflake: Snowflake) -> Self {
        CommandId::new(snowflake.0)
    }
}

impl From<CommandPermissionId> for Snowflake {
    fn from(command_permission_id: CommandPermissionId) -> Self {
        Self(command_permission_id.get())
    }
}

impl From<Snowflake> for CommandPermissionId {
    fn from(snowflake: Snowflake) -> Self {
        CommandPermissionId::new(snowflake.0)
    }
}

impl From<CommandVersionId> for Snowflake {
    fn from(command_version_id: CommandVersionId) -> Self {
        Self(command_version_id.get())
    }
}

impl From<Snowflake> for CommandVersionId {
    fn from(snowflake: Snowflake) -> Self {
        CommandVersionId::new(snowflake.0)
    }
}

impl From<TargetId> for Snowflake {
    fn from(target_id: TargetId) -> Self {
        Self(target_id.get())
    }
}

impl From<Snowflake> for TargetId {
    fn from(snowflake: Snowflake) -> Self {
        TargetId::new(snowflake.0)
    }
}

impl From<StageInstanceId> for Snowflake {
    fn from(stage_instance_id: StageInstanceId) -> Self {
        Self(stage_instance_id.get())
    }
}

impl From<Snowflake> for StageInstanceId {
    fn from(snowflake: Snowflake) -> Self {
        StageInstanceId::new(snowflake.0)
    }
}

impl From<RuleId> for Snowflake {
    fn from(rule_id: RuleId) -> Self {
        Self(rule_id.get())
    }
}

impl From<Snowflake> for RuleId {
    fn from(snowflake: Snowflake) -> Self {
        RuleId::new(snowflake.0)
    }
}

impl From<ForumTagId> for Snowflake {
    fn from(forum_tag_id: ForumTagId) -> Self {
        Self(forum_tag_id.get())
    }
}

impl From<Snowflake> for ForumTagId {
    fn from(snowflake: Snowflake) -> Self {
        ForumTagId::new(snowflake.0)
    }
}

impl From<EntitlementId> for Snowflake {
    fn from(entitlement_id: EntitlementId) -> Self {
        Self(entitlement_id.get())
    }
}

impl From<Snowflake> for EntitlementId {
    fn from(snowflake: Snowflake) -> Self {
        EntitlementId::new(snowflake.0)
    }
}

impl Serialize for Snowflake {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_str(&self.0.to_string())
    }
}

impl<'de> Deserialize<'de> for Snowflake {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        let snowflake = String::deserialize(deserializer)?;

        match snowflake.parse::<u64>() {
            Ok(snowflake) => Ok(Self(snowflake)),
            Err(error) => Err(serde::de::Error::custom(error)),
        }
    }
}
