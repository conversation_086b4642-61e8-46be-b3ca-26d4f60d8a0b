use serenity::all::Channel;
use serenity::all::ComponentInteractionDataKind;
use serenity::all::CreateInteractionResponse;
use serenity::all::CreateInteractionResponseMessage;
use serenity::all::CreateMessage;
use serenity::all::Interaction;
use serenity::all::InteractionResponseFlags;
use serenity::async_trait;
use serenity::prelude::*;
use tracing::error;
use tracing::info;
use tracing::warn;

use crate::components::TICKET_CREATE_BUTTON_ID;
use crate::components::TICKET_CREATION_CONFIRMATION_MESSAGE;
use crate::components::TICKET_EXISTS_MESSAGE;
use crate::components::support_description_embed;
use crate::state::SerenityState;
use crate::tickets;
use crate::tickets::models::TicketChangeset;
use crate::tickets::models::TicketInsert;
use crate::trengo::models::ContractId;

pub struct Handler;

#[async_trait]
impl EventHandler for Handler {
    async fn interaction_create(&self, ctx: Context, interaction: Interaction) {
        if let Interaction::Component(interaction) = &interaction {
            if let ComponentInteractionDataKind::Button = interaction.data.kind {
                if interaction.data.custom_id.as_str() != TICKET_CREATE_BUTTON_ID {
                    return;
                }

                info!("recv ticket creation request");

                let guild_id = match interaction.guild_id {
                    Some(guild_id) => guild_id,
                    None => {
                        warn!("unavailable guild id for received ticket creation request");

                        return;
                    }
                };

                let category_id = {
                    let guild = match ctx.cache.guild(guild_id) {
                        Some(guild) => guild,
                        None => {
                            warn!(
                                "unavailable guild with id: {} for received ticket request",
                                guild_id
                            );

                            return;
                        }
                    };

                    let channel = match guild.channels.get(&interaction.channel_id) {
                        Some(channel) => channel,
                        None => {
                            warn!(
                                "unavailable channel with id: {} for received ticket creation request",
                                interaction.channel_id
                            );

                            return;
                        }
                    };

                    match channel.parent_id {
                        Some(parent_id) => parent_id,
                        None => {
                            warn!(
                                "unavailable parent id for channel with id: {} for received ticket creation request",
                                interaction.channel_id
                            );

                            return;
                        }
                    }
                };

                let ctx_data = ctx.data.read().await;

                let state = match ctx_data.get::<SerenityState>() {
                    Some(state) => state,
                    None => {
                        error!("failed to get SerenityState from the data");

                        return;
                    }
                };

                match state.tickets.fetch(interaction.user.id.get().into()).await {
                    Some(mut ticket) => {
                        match ctx.http.get_channel(ticket.channel_id.into()).await.ok() {
                            Some(channel) => match channel {
                                Channel::Guild(_) => {
                                    interaction
                                        .create_response(
                                            &ctx.http,
                                            CreateInteractionResponse::Message(
                                                CreateInteractionResponseMessage::new()
                                                    .content(TICKET_EXISTS_MESSAGE)
                                                    .flags(InteractionResponseFlags::EPHEMERAL),
                                            ),
                                        )
                                        .await
                                        .ok();
                                }
                                _ => {
                                    warn!(
                                        "unavailable channel with id: {} for received ticket creation request",
                                        interaction.channel_id
                                    );

                                    return;
                                }
                            },
                            None => match tickets::utils::create_channel(
                                &ctx.http,
                                guild_id.into(),
                                interaction.user.id.into(),
                                category_id.into(),
                            )
                            .await
                            {
                                Some(ticket_channel) => {
                                    state
                                        .tickets
                                        .update(
                                            ticket.user_id,
                                            TicketChangeset {
                                                channel_id: Some(ticket_channel.id.get().into()),
                                                ..Default::default()
                                            },
                                        )
                                        .await;

                                    ticket.channel_id = ticket_channel.id.get().into();

                                    ticket_channel
                                        .send_message(
                                            &ctx.http,
                                            CreateMessage::new().embed(support_description_embed()),
                                        )
                                        .await
                                        .ok();

                                    interaction
                                        .create_response(
                                            &ctx.http,
                                            CreateInteractionResponse::Message(
                                                CreateInteractionResponseMessage::new()
                                                    .content(TICKET_CREATION_CONFIRMATION_MESSAGE)
                                                    .flags(InteractionResponseFlags::EPHEMERAL),
                                            ),
                                        )
                                        .await
                                        .ok();
                                }
                                None => {
                                    error!(
                                        "error encountered while creating ticket channel for user with id: {}",
                                        interaction.user.id.get()
                                    );

                                    return;
                                }
                            },
                        };
                    }
                    None => {
                        let ticket_channel = match tickets::utils::create_channel(
                            &ctx.http,
                            guild_id.into(),
                            interaction.user.id.into(),
                            category_id.into(),
                        )
                        .await
                        {
                            Some(channel) => channel,
                            None => {
                                error!(
                                    "unavailable newly created ticket channel for user with id: {}",
                                    interaction.user.id.get()
                                );

                                return;
                            }
                        };

                        state.tickets.insert(TicketInsert {
                            user_id: interaction.user.id,
                            channel_id: ticket_channel.clone().into(),
                            category_id: category_id.clone().into(),
                            contact_name: interaction.user.name.clone().into(),
                            contact_id: ContractId::from(interaction.user.id),
                        });

                        ticket_channel
                            .send_message(
                                &ctx.http,
                                CreateMessage::new().embed(support_description_embed()),
                            )
                            .await
                            .ok();

                        interaction
                            .create_response(
                                &ctx.http,
                                CreateInteractionResponse::Message(
                                    CreateInteractionResponseMessage::new()
                                        .content(TICKET_CREATION_CONFIRMATION_MESSAGE)
                                        .flags(InteractionResponseFlags::EPHEMERAL),
                                ),
                            )
                            .await
                            .ok();
                    }
                }

                info!(
                    "ticket creation request handled successfully for user with id: {}",
                    interaction.user.id.get()
                );
            }
        }
    }
}
