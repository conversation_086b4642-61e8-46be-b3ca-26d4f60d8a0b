use serde::{Deserialize, Serialize};
use serenity::all::UserId;
use std::{
    fmt::Display,
    ops::{Deref, DerefMut},
};

use crate::trengo::utils;

const TRENGO_CONTACT_ID_LIMIT: usize = 20;
const PREFIX: &str = "custom-";
const POSTFIX_CHAR: &str = "-";

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct ContractId(String);

impl ContractId {
    pub fn new(contract_id: &str) -> Option<Self> {
        if contract_id.starts_with(PREFIX) && contract_id.len() <= TRENGO_CONTACT_ID_LIMIT {
            return Some(Self(contract_id.to_string()));
        }

        None
    }
}

impl Display for ContractId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl Serialize for ContractId {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        serializer.serialize_str(&self.0)
    }
}

impl<'de> Deserialize<'de> for ContractId {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let contract_id = String::deserialize(deserializer)?;

        Self::new(&contract_id).ok_or_else(|| {
            serde::de::Error::custom(format!(
                "invalid contract id must start with '{PREFIX}' and be at most {TRENGO_CONTACT_ID_LIMIT} characters long",
            ))
        })
    }
}

impl From<UserId> for ContractId {
    fn from(user_id: UserId) -> Self {
        let encoded_id = utils::base62::encode(user_id.get());

        let needed = TRENGO_CONTACT_ID_LIMIT - encoded_id.len() - PREFIX.len();

        let encoded = if needed > 0 {
            format!("{PREFIX}{encoded_id}{}", POSTFIX_CHAR.repeat(needed))
        } else {
            format!("{PREFIX}{encoded_id}")
        };

        Self(encoded)
    }
}

impl From<ContractId> for UserId {
    fn from(contract_id: ContractId) -> Self {
        let id = contract_id
            .0
            .trim_start_matches(PREFIX)
            .trim_end_matches(POSTFIX_CHAR);

        utils::base62::decode(id).unwrap().into()
    }
}

impl From<String> for ContractId {
    fn from(contract_id: String) -> Self {
        Self::new(&contract_id).expect(
            "invalid contract id must start with '{PREFIX}' and be at most {TRENGO_CONTACT_ID_LIMIT} characters long",
        )
    }
}

impl From<&str> for ContractId {
    fn from(contract_id: &str) -> Self {
        Self::new(contract_id).expect(
            "invalid contract id must start with '{PREFIX}' and be at most {TRENGO_CONTACT_ID_LIMIT} characters long",
        )
    }
}

impl Deref for ContractId {
    type Target = String;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl DerefMut for ContractId {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}

#[derive(Debug, Clone, Default)]

pub struct ContractName(String);

impl ContractName {
    pub fn new(contract_name: &str) -> Self {
        Self(contract_name.to_string())
    }
}

impl Display for ContractName {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl Serialize for ContractName {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        serializer.serialize_str(&self.0)
    }
}

impl<'de> Deserialize<'de> for ContractName {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let contract_name = String::deserialize(deserializer)?;

        Ok(Self::new(&contract_name))
    }
}

impl From<String> for ContractName {
    fn from(contract_name: String) -> Self {
        Self::new(&contract_name)
    }
}

impl From<&str> for ContractName {
    fn from(contract_name: &str) -> Self {
        Self::new(contract_name)
    }
}

impl Deref for ContractName {
    type Target = String;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl DerefMut for ContractName {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}
