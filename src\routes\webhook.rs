// TODO: use channel topics as persistence, use advanced cache with lru, ttl, and periodic cleanup; use ENV.guild_id in webhook

use rocket::{State, futures::future::join_all, post, serde::json::Json};
use serde::Deserialize;
use serenity::all::{ChannelId, CreateAttachment, CreateMessage, UserId};
use tracing::{debug, error, info, warn};

use crate::{
    state::RocketState,
    tickets::{self, TicketChangeset},
    trengo::{self, models::ContractId},
};

#[derive(Debug, Deserialize)]
pub struct WebhookPayload<'a> {
    message_id: &'a str,
    ticket_id: &'a str,
    contact_identifier: Option<&'a str>,
}

#[post("/webhook", format = "json", data = "<payload>")]
pub async fn handler<'a>(payload: Json<WebhookPayload<'a>>, state: &State<RocketState>) {
    // TODO: verify signature of payload

    info!("recv /webhook with payload: {:?}", payload);

    if payload.contact_identifier.is_none() {
        return;
    }

    let contract_id = ContractId::new(payload.contact_identifier.unwrap());

    if let None = contract_id {
        debug!(
            "recv /webhook for non-custom channel with payload: {:?}",
            payload
        );

        return;
    }

    let user_id = UserId::from(contract_id.unwrap());

    info!(
        "recv for transport of message to discord ticket channel for user with id: {}",
        user_id
    );

    let mut ticket = match state.tickets.fetch(user_id.into()).await {
        Some(ticket) => ticket,
        None => {
            warn!(
                "unavailable ticket for user with id: {} for transport of message to discord ticket channel",
                user_id
            );

            return;
        }
    };

    let guild = match state.discord_http.get_guild(state.guild_id).await.ok() {
        Some(guild) => guild,
        None => {
            warn!(
                "unavailable guild with id: {} for transport of message to discord ticket channel",
                state.guild_id
            );

            return;
        }
    };

    if let Some(mut channels) = guild.channels(&state.discord_http).await.ok() {
        if !channels.contains_key(&ChannelId::new(ticket.channel_id.into())) {
            let channel = tickets::utils::create_channel(
                &state.discord_http,
                state.guild_id,
                ticket.user_id,
                ticket.category_id,
            )
            .await;

            if channel.is_none() {
                error!(
                    "error ticket channel for user with id: {} was not created for request to transport message to discord",
                    ticket.user_id
                );

                return;
            }

            let new_channel = channel.unwrap();

            state.tickets.update(
                ticket.user_id,
                TicketChangeset {
                    channel_id: Some(new_channel.id.get().into()),
                    ..Default::default()
                },
            );

            ticket.channel_id = new_channel.id.get().into();

            channels.insert(new_channel.id, new_channel);
        }

        let channel = match channels.get(&ticket.channel_id.into()) {
            Some(channel) => channel,
            None => {
                error!(
                    "error ticket channel for user with id: {} was not found for request to transport message to discord",
                    ticket.user_id
                );

                return;
            }
        };

        match trengo::fetch_message(payload.ticket_id, payload.message_id).await {
            Some(message) => {
                let attachments = join_all(
                    message
                        .attachment_urls
                        .into_iter()
                        .map(async |url| CreateAttachment::url(&state.discord_http, &url).await),
                )
                .await
                .into_iter()
                .filter(|attachment| attachment.is_ok())
                .map(|attachment| attachment.unwrap())
                .collect::<Vec<_>>();

                let builder = CreateMessage::new()
                    .content(message.content)
                    .add_files(attachments);

                match channel.send_message(&state.discord_http, builder).await {
                    Ok(message) => {
                        info!(
                            "handled message transport to discord for user with id: {} successfully, message: {:?}",
                            ticket.user_id, message
                        );
                    }
                    Err(why) => {
                        error!(
                            "error encountered while transporting message to discord for user with id: {}: {:?}",
                            ticket.user_id, why
                        );
                    }
                }
            }
            None => {
                error!(
                    "error encountered while fetching message from trengo for transport to discord, user with id: {}",
                    ticket.user_id
                );
            }
        }
    }
}
