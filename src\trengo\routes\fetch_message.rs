use reqwest::StatusCode;
use serde::Deserialize;

use crate::trengo::{BASE_URL, CLIENT};

#[derive(Debug)]
pub struct Message {
    pub content: String,
    pub attachment_urls: Vec<String>,
}

#[derive(Debug, Deserialize)]
struct MessageJson {
    pub message: String,
    pub attachments: Vec<AttachmentJson>,
}

#[derive(Debug, Deserialize)]
struct AttachmentJson {
    pub full_url: String,
}

/// fetches a message from a ticket in trengo.
///
/// # Returns
///
/// Some if the message was fetched successfully.
///
/// None if the request failed for any reason.
pub async fn fetch_message(ticket_id: &str, message_id: &str) -> Option<Message> {
    if let Some(response) = CLIENT
        .get(format!("{BASE_URL}/tickets/{ticket_id}/messages/{message_id}").as_str())
        .send()
        .await
        .ok()
    {
        if response.status() != StatusCode::OK {
            return None;
        }

        if let Some(json) = response.json::<MessageJson>().await.ok() {
            return Some(Message {
                content: json.message,
                attachment_urls: json
                    .attachments
                    .into_iter()
                    .map(|attachment| attachment.full_url)
                    .collect(),
            });
        }
    }

    None
}
